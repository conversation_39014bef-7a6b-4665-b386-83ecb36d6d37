# Определяем трансформации
transform codex_appear:
    alpha 0.0
    yoffset 50
    pause 0.2
    parallel:
        ease 0.7 yoffset 0
    parallel:
        ease 0.7 alpha 1.0

transform codex_hover_effect:
    on idle:
        ease 0.3 alpha 1.0
    on hover:
        ease 0.3 alpha 0.8

transform hover_with_ease:
    on idle:
        ease 0.3 alpha 1.0
    on hover:
        ease 0.3 alpha 0.8

# Экран кодекса
screen codex():
    tag menu

    add "black"

    add "assets/gallery/images/main/background.png" ypos 138

    frame:
        background "assets/settings/images/logo.png" 
        xpos 853 ypos 40
        xysize (214, 65)
        text _("CODEX") align (0.5, 0.5) size 16 font "fonts/Montserrat-Black.ttf"

    add "assets/settings/images/top_line.png" xpos 55 ypos 138

    # Добавляем изображение Codex
    frame at codex_appear:
        background None
        xpos 55 ypos 178
        xysize (429, 716)
        
        imagebutton at hover_with_ease:
            idle "assets/gallery/images/main/codex-return.png"
            hover "assets/gallery/images/main/codex-return.png"
            action ShowMenu("main")
            xsize 429 ysize 716
        
        text _("Return to Gallery") xalign 0.5 ypos 665 size 18 font "fonts/Montserrat-Medium.ttf" color "#000000"
    
    # Добавляем сетку персонажей
    frame at codex_appear:
        background None
        xpos 509  # 55 + 429 + 25 (отступ от карточки codex)
        ypos 178
        xysize (1356, 726)  # Ширина достаточная для 5 карточек с отступами
        
        viewport:
            mousewheel True
            draggable True
            scrollbars None  # Скрываем скроллбар, но сохраняем функциональность
            
            grid 5 3:  # Изменяем с 5x2 на 5x3 для добавления дополнительного ряда
                xspacing 15
                yspacing 10
                
                # Кнопка Nicole
                frame:
                    background None
                    xysize (249, 335)
                    
                    imagebutton at codex_hover_effect:
                        idle "assets/gallery/images/codex/nicole.png"
                        hover "assets/gallery/images/codex/nicole.png"
                        action ShowMenu("nicole")
                        xsize 249 ysize 335
                    
                    text _("Nicole") xpos 10 yalign 0.95 size 18 font "fonts/Montserrat-Bold.ttf"
                
                # Заполняем остальные ячейки недоступными персонажами
                for i in range(14):  # Увеличиваем количество блоков с 9 до 14
                    frame:
                        background None
                        xysize (249, 335)
                        
                        add "assets/gallery/images/codex/close.png" xsize 249 ysize 335

    frame at slide_from_left:
        background "assets/menu/images/report_bg.svg"
        xysize (354, 57)
        xpos 55 ypos 40

        add "assets/menu/images/report_icon.svg" xpos 18 yalign 0.5
        text _("Did you find a bug?") xpos 47 yalign 0.5 size 16 font "fonts/Montserrat-Medium.ttf"

        button at button_alpha: 
            background 'assets/menu/images/report_button.svg'
            xysize (90, 34)
            xpos 246 yalign 0.5
            text _("Report") color "#000000" size 16 align (0.5, 0.5) font "fonts/Montserrat-SemiBold.ttf"
            action Start("")

    frame at slide_from_right:
        background None
        xysize (232, 48)
        xpos 1620 ypos 40

        hbox:
            spacing 10

            default bonus_button = None
            button:
                background None
                padding (0, 0)

                action ShowMenu("bonus")
                hovered SetLocalVariable('bonus_button', 'HOVERED')
                unhovered SetLocalVariable('bonus_button', 'UNHOVERED')

                has fixed
                fit_first True

                image "assets/menu/images/bonus_button.png"
                image "assets/menu/images/bonus_button_h.png":
                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 alpha 1.0
                    elif bonus_button == 'UNHOVERED':
                        at transform:
                            easeout .5 alpha .0
                    else:
                        at transform:
                            alpha .0

                vbox:
                    xalign 0.5
                    yalign 0.5

                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#ffffff")
                    else:
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#bdbdbd")
                    hbox:
                        spacing 5
                        add "assets/menu/images/bonus_icon.png"
                        text _("BONUS CONTENT") size 14 font "fonts/Montserrat-Medium.ttf"

            button at button_alpha:
                background "assets/menu/images/atch_button.png"
                xysize (48, 48)
                action ShowMenu("achievements")

    use navigation()
