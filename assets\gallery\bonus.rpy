# Определяем трансформации
transform bonus_appear:
    alpha 0.0
    yoffset 50
    pause 0.2
    parallel:
        ease 0.7 yoffset 0
    parallel:
        ease 0.7 alpha 1.0

transform bonus_hover_effect:
    on idle:
        ease 0.3 alpha 1.0
    on hover:
        ease 0.3 alpha 0.8

transform hover_with_ease:
    on idle:
        ease 0.3 alpha 1.0
    on hover:
        ease 0.3 alpha 0.8

# Экран бонусов
screen bonus():
    tag menu

    add "black"

    add "assets/gallery/images/main/background.png" ypos 138

    frame:
        background "assets/settings/images/logo.png" 
        xpos 853 ypos 40
        xysize (214, 65)
        text _("GALLERY") align (0.5, 0.5) size 16 font "fonts/Montserrat-Black.ttf"
        text _("BONUS") xalign 0.5 ypos 35 size 32 font "fonts/Montserrat-Black.ttf" color "#F6DD51"

    # Добавляем подзаголовок Bonus с цветом #F6DD51
    frame:
        background None
        xpos 853 ypos 105
        xysize (214, 30)

    add "assets/settings/images/top_line.png" xpos 55 ypos 138

    # Добавляем кнопку Return to Gallery
    frame at bonus_appear:
        background None
        xpos 55 ypos 178
        xysize (228, 59)
        
        imagebutton at hover_with_ease:
            idle "assets/gallery/images/bonus/return.png"
            hover "assets/gallery/images/bonus/return.png"
            action ShowMenu("main")
            xsize 228 ysize 59
        
        fixed:
            xysize (228, 59)
            text _("Return to Gallery") xpos 114 ypos 30 size 16 font "fonts/Montserrat-Medium.ttf" color "#1a1a1a" anchor (0.5, 0.5)

    # Добавляем блок информации об открытых бонусах
    frame at bonus_appear:
        background "assets/gallery/images/bonus/info_unlocked.png"
        xpos 1716 ypos 178
        xysize (148, 59)
        
        hbox:
            spacing 10
            align (0.5, 0.5)
            add "assets/gallery/images/bonus/icon-lock.png" yalign 0.5
            text _("10/20") size 16 font "fonts/Montserrat-Medium.ttf" color "#F6DD51" yalign 0.5

    # Добавляем горизонтальную grid сетку с бонусным контентом
    frame at bonus_appear:
        background None
        xpos 55 ypos 260  # Позиция ниже кнопки Return
        xysize (1809, 600)  # Достаточная высота для нескольких рядов
        
        viewport:
            mousewheel True
            draggable True
            scrollbars None  # Скрываем полосу прокрутки, но оставляем функциональность
            xysize (1809, 650)
            
            grid 4 5:  # 4 блока в ряд, 5 рядов (всего 20 блоков)
                xspacing 10  # Горизонтальный отступ между блоками
                yspacing 15  # Вертикальный отступ между рядами
                
                # Первый ряд (1 разблокированный, 3 заблокированных)
                
                # Первый блок (разблокированный)
                frame:
                    background None
                    xysize (441, 293)  # Обновленный размер блока
                    
                    imagebutton at hover_with_ease:
                        idle "assets/gallery/images/bonus/unlocked.png"
                        hover "assets/gallery/images/bonus/unlocked.png"
                        action NullAction()  # Пока без действия
                        xsize 441 ysize 293
                    
                    # Добавляем иконку изображения по центру
                    add "assets/gallery/images/bonus/image.png" align (0.5, 0.5)
                
                # Второй блок (заблокированный)
                frame:
                    background None
                    xysize (441, 293)
                    
                    imagebutton at hover_with_ease:
                        idle "assets/gallery/images/bonus/locked.png"
                        hover "assets/gallery/images/bonus/locked.png"
                        action NullAction()
                        xsize 441 ysize 293
                
                # Третий блок (заблокированный)
                frame:
                    background None
                    xysize (441, 293)
                    
                    imagebutton at hover_with_ease:
                        idle "assets/gallery/images/bonus/locked.png"
                        hover "assets/gallery/images/bonus/locked.png"
                        action NullAction()
                        xsize 441 ysize 293
                
                # Четвертый блок (заблокированный)
                frame:
                    background None
                    xysize (441, 293)
                    
                    imagebutton at hover_with_ease:
                        idle "assets/gallery/images/bonus/locked.png"
                        hover "assets/gallery/images/bonus/locked.png"
                        action NullAction()
                        xsize 441 ysize 293
                
                # Второй ряд (все заблокированы)
                for i in range(4):
                    frame:
                        background None
                        xysize (441, 293)
                        
                        imagebutton at hover_with_ease:
                            idle "assets/gallery/images/bonus/locked.png"
                            hover "assets/gallery/images/bonus/locked.png"
                            action NullAction()
                            xsize 441 ysize 293
                
                # Третий ряд (все заблокированы)
                for i in range(4):
                    frame:
                        background None
                        xysize (441, 293)
                        
                        imagebutton at hover_with_ease:
                            idle "assets/gallery/images/bonus/locked.png"
                            hover "assets/gallery/images/bonus/locked.png"
                            action NullAction()
                            xsize 441 ysize 293
                
                # Четвертый ряд (все заблокированы)
                for i in range(4):
                    frame:
                        background None
                        xysize (441, 293)
                        
                        imagebutton at hover_with_ease:
                            idle "assets/gallery/images/bonus/locked.png"
                            hover "assets/gallery/images/bonus/locked.png"
                            action NullAction()
                            xsize 441 ysize 293
                
                # Пятый ряд (все заблокированы)
                for i in range(4):
                    frame:
                        background None
                        xysize (441, 293)
                        
                        imagebutton at hover_with_ease:
                            idle "assets/gallery/images/bonus/locked.png"
                            hover "assets/gallery/images/bonus/locked.png"
                            action NullAction()
                            xsize 441 ysize 293

    frame at slide_from_left:
        background "assets/menu/images/report_bg.svg"
        xysize (354, 57)
        xpos 55 ypos 40

        add "assets/menu/images/report_icon.svg" xpos 18 yalign 0.5
        text _("Did you find a bug?") xpos 47 yalign 0.5 size 16 font "fonts/Montserrat-Medium.ttf"

        button at button_alpha: 
            background 'assets/menu/images/report_button.svg'
            xysize (90, 34)
            xpos 246 yalign 0.5
            text _("Report") color "#000000" size 16 align (0.5, 0.5) font "fonts/Montserrat-SemiBold.ttf"
            action Start("")

    frame at slide_from_right:
        background None
        xysize (232, 48)
        xpos 1620 ypos 40

        hbox:
            spacing 10

            default bonus_button = None
            button:
                background None
                padding (0, 0)

                action ShowMenu("bonus")
                hovered SetLocalVariable('bonus_button', 'HOVERED')
                unhovered SetLocalVariable('bonus_button', 'UNHOVERED')

                has fixed
                fit_first True

                image "assets/menu/images/bonus_button.png"
                image "assets/menu/images/bonus_button_h.png":
                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 alpha 1.0
                    elif bonus_button == 'UNHOVERED':
                        at transform:
                            easeout .5 alpha .0
                    else:
                        at transform:
                            alpha .0

                vbox:
                    xalign 0.5
                    yalign 0.5

                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#ffffff")
                    else:
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#bdbdbd")
                    hbox:
                        spacing 5
                        add "assets/menu/images/bonus_icon.png"
                        text _("BONUS CONTENT") size 14 font "fonts/Montserrat-Medium.ttf"

            button at button_alpha:
                background "assets/menu/images/atch_button.png"
                xysize (48, 48)
                action ShowMenu("achievements")

    use navigation()
