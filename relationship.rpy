label relationship:
    hide screen ui_button_overlay
    call screen relationship

transform semi_transparent:
    alpha 0.7

transform slide_from_right_return:
    xpos 2000
    easein 1.0 xpos 1830

transform dissolve_in:
    alpha 0.0
    linear 0.4 alpha 1.0

transform dissolve_out:
    alpha 1.0
    linear 0.4 alpha 0.0

transform flip_horizontal:
    xzoom -1

default names = ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
default names_big = ["AMBER", "MARG<PERSON>", "RUB<PERSON>", "NICOLE", "ELISE", "ANGELINA", "VALARIE", "ANASTASIA", "LAURA", "VIOLET"]
default love_current = [2, 3, 4, 1, 1, 1, 3, 1, 2, 1]
default love_max = [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]
default ntr_current = [1, 2, 1, 1, 4, 1, 1, 3, 1, 5]
default ntr_max = [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]
default statusRange = [3, 6, 10, 2, 7, 1, 8, 9, 4, 5] #1-10
default playerChose = 0

screen relationship:
    #background
    add "gui/Codex/bg.png"

    #res_stats
    frame:
        background "assets/settings/images/logo.png" 
        xpos 853 ypos 50
        xysize (214, 65)
        text _("RELATIONSHIP\nSTATS") align (0.5, 0.5) size 16 font "fonts/Montserrat-Black.ttf" text_align 0.5


    #return
    frame at slide_from_right_return:
        background None
        xalign 0.95
        yalign 0.05
        xsize 130
        ysize 58
        imagebutton at hover_effect:
            idle "gui/Codex/return.png"
            hover "gui/Codex/return.png"
            action Return()
            xsize 165 ysize 222
        text _("Return") xalign 0.6 yalign 0.7 size 15 font "fonts/Montserrat-Medium.ttf" color "#060606"

    #name
    frame at slide_from_left:
        ypos 0.05
        background None
        text _(names_big[playerChose]) xalign 0.05 yalign 0.05 size 40 font "fonts/Montserrat-Black.ttf"

    #left_full_images
    frame at gallery_button_appear(0.1):
        xalign 0.05
        yalign 0.75
        background None
        if playerChose == 0:
            add "gui/Codex/1_b.png" at dissolve_in yalign 0.75 xalign 0.05
        elif playerChose == 1:
            add "gui/Codex/2_b.png" at dissolve_in yalign 0.75 xalign 0.05
        elif playerChose == 2:
            add "gui/Codex/3_b.png" at dissolve_in yalign 0.75 xalign 0.05
        elif playerChose == 3:
            add "gui/Codex/4_b.png" at dissolve_in yalign 0.75 xalign 0.05
        elif playerChose == 4:
            add "gui/Codex/5_b.png" at dissolve_in yalign 0.75 xalign 0.05
        elif playerChose == 5:
            add "gui/Codex/6_b.png" at dissolve_in yalign 0.75 xalign 0.05
        elif playerChose == 6:
            add "gui/Codex/7_b.png" at dissolve_in yalign 0.75 xalign 0.05
        elif playerChose == 7:
            add "gui/Codex/8_b.png" at dissolve_in yalign 0.75 xalign 0.05
        elif playerChose == 8:
            add "gui/Codex/9_b.png" at dissolve_in yalign 0.75 xalign 0.05
        elif playerChose == 9:
            add "gui/Codex/10_b.png" at dissolve_in yalign 0.75 xalign 0.05

    #Info
    hbox:
        xpos 730
        ypos 160
        xanchor 0.0
        yanchor 0.0
        spacing 40
        #Love Stage
        frame at gallery_button_appear(0.1):
            xsize 534
            ysize 80
            background None
            add "gui/Codex/bar_bg.png"
            text _("Love Stage") xalign 0.1 yalign 0.7 size 16 font "fonts/Montserrat-SemiBold.ttf" at semi_transparent
            text " {size=24}[love_current[playerChose]]{/size}   /   [love_max[playerChose]]" xalign 0.95 yalign 0.7 size 16 font "fonts/Montserrat-SemiBold.ttf" at semi_transparent

        #NTR Stage
        frame at gallery_button_appear(0.2):
            xsize 514
            ysize 80
            background None
            add "gui/Codex/bar_bg.png"
            text _("NTR Stage") xalign 0.1 yalign 0.7 size 16 font "fonts/Montserrat-SemiBold.ttf" at semi_transparent
            text " {size=24}[ntr_current[playerChose]]{/size}   /   [ntr_max[playerChose]]" xalign 0.95 yalign 0.7 size 16 font "fonts/Montserrat-SemiBold.ttf" at semi_transparent

    hbox:
        xpos 730
        ypos 280
        xanchor 0.0
        yanchor 0.0
        spacing 70
        frame at gallery_button_appear(0.3):
            xsize 1132
            ysize 80
            background None
            add "gui/Codex/rate_bg.png"
            text _("Love") xalign 0.03 yalign 0.65 size 16 font "fonts/Montserrat-SemiBold.ttf" at semi_transparent
            text _("NTR") xalign 0.96 yalign 0.65 size 16 font "fonts/Montserrat-SemiBold.ttf" at semi_transparent
            #love
            imagemap:
                idle "gui/Codex/unknown.png"
                ypos 34
                xpos 557
                xanchor 1.0
                bar value love_current[playerChose] range 5 xysize (460, 10) xalign 1.0 yalign 0.5  at flip_horizontal
            #ntr
            imagemap:
                idle "gui/Codex/unknown.png"
                ypos 34
                xpos 557
                xanchor 0.0
                bar value ntr_current[playerChose] range 5 xysize (460, 10) xalign 0.0 yalign 0.5 
            imagemap:
                idle "gui/Codex/rate_bg_line.png"

    #gap
    add "gui/Codex/gap.png" xpos 740 ypos 410 xsize 1110


    #Character List
    vbox:
        xpos 730
        ypos 450
        xanchor 0.0
        yanchor 0.0
        xsize 800
        spacing 30
        hbox:
            spacing 40
            frame at gallery_button_appear(0.4):
                background None
                xysize (165, 222)
                
                imagebutton at hover_effect:
                    if playerChose == 0:
                        idle "gui/Codex/1_s_h_s.png"
                    else:
                        idle "gui/Codex/1_s.png"
                    if playerChose == 0:
                        hover "gui/Codex/1_s_h_s.png"
                    else:
                        hover "gui/Codex/1_s_h.png"
                    action SetVariable("playerChose", 0)
                    xsize 165 ysize 222
                
                vbox:
                    xpos 20
                    yalign 0.95
                    spacing 15
                    
                    text _(names[0]) xalign 0.11 yalign 1.0 size 16 font "fonts/Montserrat-Bold.ttf"

            frame at gallery_button_appear(0.5):
                background None
                xysize (165, 222)
                
                imagebutton at hover_effect:
                    if playerChose == 1:
                        idle "gui/Codex/2_s_h_s.png"
                    else:
                        idle "gui/Codex/2_s.png"
                    if playerChose == 1:
                        hover "gui/Codex/2_s_h_s.png"
                    else:
                        hover "gui/Codex/2_s_h.png"
                    action SetVariable("playerChose", 1)
                    xsize 165 ysize 222
                
                vbox:
                    xpos 20
                    yalign 0.95
                    spacing 15
                    
                    text _(names[1]) xalign 0.11 yalign 1.0 size 16 font "fonts/Montserrat-Bold.ttf"

            frame at gallery_button_appear(0.6):
                background None
                xysize (165, 222)
                
                imagebutton at hover_effect:
                    if playerChose == 2:
                        idle "gui/Codex/3_s_h_s.png"
                    else:
                        idle "gui/Codex/3_s.png"
                    if playerChose == 2:
                        hover "gui/Codex/3_s_h_s.png"
                    else:
                        hover "gui/Codex/3_s_h.png"
                    action SetVariable("playerChose", 2)
                    xsize 165 ysize 222
                
                vbox:
                    xpos 20
                    yalign 0.95
                    spacing 15
                    
                    text _(names[2]) xalign 0.11 yalign 1.0 size 16 font "fonts/Montserrat-Bold.ttf"

            frame at gallery_button_appear(0.7):
                background None
                xysize (165, 222)
                
                imagebutton at hover_effect:
                    if playerChose == 3:
                        idle "gui/Codex/4_s_h_s.png"
                    else:
                        idle "gui/Codex/4_s.png"
                    if playerChose == 3:
                        hover "gui/Codex/4_s_h_s.png"
                    else:
                        hover "gui/Codex/4_s_h.png"
                    action SetVariable("playerChose", 3)
                    xsize 165 ysize 222
                
                vbox:
                    xpos 20
                    yalign 0.95
                    spacing 15
                    
                    text _(names[3]) xalign 0.11 yalign 1.0 size 16 font "fonts/Montserrat-Bold.ttf"

            frame at gallery_button_appear(0.8):
                background None
                xysize (165, 222)
                
                imagebutton at hover_effect:
                    if playerChose == 4:
                        idle "gui/Codex/5_s_h_s.png"
                    else:
                        idle "gui/Codex/5_s.png"
                    if playerChose == 4:
                        hover "gui/Codex/5_s_h_s.png"
                    else:
                        hover "gui/Codex/5_s_h.png"
                    action SetVariable("playerChose", 4)
                    xsize 165 ysize 222
                
                vbox:
                    xpos 20
                    yalign 0.95
                    spacing 15
                    
                    text _(names[4]) xalign 0.11 yalign 1.0 size 16 font "fonts/Montserrat-Bold.ttf"


        hbox:
            spacing 40
            frame at gallery_button_appear(0.9):
                background None
                xysize (165, 222)
                
                imagebutton at hover_effect:
                    if playerChose == 5:
                        idle "gui/Codex/6_s_h_s.png"
                    else:
                        idle "gui/Codex/6_s.png"
                    if playerChose == 5:
                        hover "gui/Codex/6_s_h_s.png"
                    else:
                        hover "gui/Codex/6_s_h.png"
                    action SetVariable("playerChose", 5)
                    xsize 165 ysize 222
                
                vbox:
                    xpos 20
                    yalign 0.95
                    spacing 15
                    
                    text _(names[5]) xalign 0.11 yalign 1.0 size 16 font "fonts/Montserrat-Bold.ttf"

            frame at gallery_button_appear(1.0):
                background None
                xysize (165, 222)
                
                imagebutton at hover_effect:
                    if playerChose == 6:
                        idle "gui/Codex/7_s_h_s.png"
                    else:
                        idle "gui/Codex/7_s.png"
                    if playerChose == 6:
                        hover "gui/Codex/7_s_h_s.png"
                    else:
                        hover "gui/Codex/7_s_h.png"
                    action SetVariable("playerChose", 6)
                    xsize 165 ysize 222
                
                vbox:
                    xpos 20
                    yalign 0.95
                    spacing 15
                    
                    text _(names[6]) xalign 0.11 yalign 1.0 size 16 font "fonts/Montserrat-Bold.ttf"

            frame at gallery_button_appear(1.1):
                background None
                xysize (165, 222)
                
                imagebutton at hover_effect:
                    if playerChose == 7:
                        idle "gui/Codex/8_s_h_s.png"
                    else:
                        idle "gui/Codex/8_s.png"
                    if playerChose == 7:
                        hover "gui/Codex/8_s_h_s.png"
                    else:
                        hover "gui/Codex/8_s_h.png"
                    action SetVariable("playerChose", 7)
                    xsize 165 ysize 222
                
                vbox:
                    xpos 20
                    yalign 0.95
                    spacing 15
                    
                    text _(names[7]) xalign 0.11 yalign 1.0 size 16 font "fonts/Montserrat-Bold.ttf"

            frame at gallery_button_appear(1.2):
                background None
                xysize (165, 222)
                
                imagebutton at hover_effect:
                    if playerChose == 8:
                        idle "gui/Codex/9_s_h_s.png"
                    else:
                        idle "gui/Codex/9_s.png"
                    if playerChose == 8:
                        hover "gui/Codex/9_s_h_s.png"
                    else:
                        hover "gui/Codex/9_s_h.png"
                    action SetVariable("playerChose", 8)
                    xsize 165 ysize 222
                
                vbox:
                    xpos 20
                    yalign 0.95
                    spacing 15
                    
                    text _(names[8]) xalign 0.11 yalign 1.0 size 16 font "fonts/Montserrat-Bold.ttf"

            frame at gallery_button_appear(1.3):
                background None
                xysize (165, 222)
                
                imagebutton at hover_effect:
                    if playerChose == 9:
                        idle "gui/Codex/10_s_h_s.png"
                    else:
                        idle "gui/Codex/10_s.png"
                    if playerChose == 9:
                        hover "gui/Codex/10_s_h_s.png"
                    else:
                        hover "gui/Codex/10_s_h.png"
                    action SetVariable("playerChose", 9)
                    xsize 165 ysize 222
                
                vbox:
                    xpos 20
                    yalign 0.95
                    spacing 15
                    
                    text _(names[9]) xalign 0.11 yalign 1.0 size 16 font "fonts/Montserrat-Bold.ttf"

