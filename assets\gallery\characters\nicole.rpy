# Экран перс<PERSON><PERSON><PERSON><PERSON> <PERSON>
transform nicole_appear:
    alpha 0.0
    yoffset 50
    pause 0.2
    parallel:
        ease 0.7 yoffset 0
    parallel:
        ease 0.7 alpha 1.0

screen nicole():
    tag menu

    add "black"


    frame:
        background "assets/settings/images/logo.png" 
        xpos 853 ypos 40
        xysize (214, 65)
        text _("NICOLE") align (0.5, 0.5) size 16 font "fonts/Montserrat-Black.ttf"

    add "assets/settings/images/top_line.png" xpos 55 ypos 138

    # Добавляем полное изображение Nicole
    frame at nicole_appear:
        background None
        xpos 55 ypos 178
        xysize (833, 716)
        
        add "assets/gallery/images/codex/nicole-full.png" xsize 833 ysize 716
    
    # Добавляем информацию о персонаже справа
    frame at nicole_appear:
        background None
        xpos 930
        ypos 190
        xysize (652, 716)
        
        vbox:
            spacing 10
            
            text _("Character name") size 14 font "fonts/Montserrat-Medium.ttf" color "#9FA0A3"
            text _("Nicole") size 32 font "fonts/Montserrat-Bold.ttf"
    
    # Добавляем информацию о возрасте
    frame at nicole_appear:
        background "assets/gallery/images/codex/age.png"
        xpos 1584
        ypos 190  # Выравниваем по верхнему краю с именем
        xysize (109, 59)
        
        text _("Age: 20") align (0.5, 0.5) size 16 font "fonts/Montserrat-Medium.ttf" color "#BDBDBD"
    
    # Добавляем информацию о фракции
    frame at nicole_appear:
        background "assets/gallery/images/codex/faction.png"
        xpos 1702
        ypos 190  # Выравниваем по верхнему краю с именем
        xysize (163, 59)
        
        text _("Faction: TRIAD") align (0.5, 0.5) size 16 font "fonts/Montserrat-Medium.ttf" color "#9D0208"
    
    # Добавляем линию
    frame at nicole_appear:
        background "assets/gallery/images/codex/line.png"
        xpos 934
        ypos 293
        xysize (930, 2)
    
    # Добавляем описание персонажа
    frame at nicole_appear:
        background None
        xpos 934
        ypos 315
        xysize (930, 579)
        
        text _("""Before her shining, Nicole had several marriages with rich men, but she wasn't in it for their money. She's smart and educated, working as a respected doctor. A journey through academia shaped her into a distinguished doctor, renowned for her skill and compassion.
She works doctor now in the Capital. In the community, she's highly regarded, like the top person. But watch out—if you upset her, she might not help you when you need it most. Best to not get in her way or you will die from a simple illness.""") size 18 font "fonts/Montserrat-Medium.ttf" color "#BDBDBD" xmaximum 930 line_spacing 10
    
    # Добавляем кнопку Return
    frame at nicole_appear:
        background None
        xpos 934
        ypos 835
        xysize (930, 59)
        
        # Используем переменную для отслеживания состояния ховера
        $ hover_state = False
        
        # Применяем эффект ховера с плавным переходом
        imagebutton at hover_with_ease:
            idle "assets/gallery/images/codex/return.png"
            hover "assets/gallery/images/codex/return.png"
            action ShowMenu("codex")
            xysize (930, 59)
            hovered SetLocalVariable("hover_state", True)
            unhovered SetLocalVariable("hover_state", False)
        
        # Добавляем текст поверх кнопки, центрируя его
        fixed:
            xysize (930, 59)
            text _("Return to Codex") align (0.5, 0.5) size 18 font "fonts/Montserrat-Medium.ttf" color ("#FFFFFF" if hover_state else "#BDBDBD")
    
    frame at slide_from_left:
        background "assets/menu/images/report_bg.svg"
        xysize (354, 57)
        xpos 55 ypos 40

        add "assets/menu/images/report_icon.svg" xpos 18 yalign 0.5
        text _("Did you find a bug?") xpos 47 yalign 0.5 size 16 font "fonts/Montserrat-Medium.ttf"

        button at button_alpha: 
            background 'assets/menu/images/report_button.svg'
            xysize (90, 34)
            xpos 246 yalign 0.5
            text _("Report") color "#000000" size 16 align (0.5, 0.5) font "fonts/Montserrat-SemiBold.ttf"
            action Start("")

    frame at slide_from_right:
        background None
        xysize (232, 48)
        xpos 1620 ypos 40

        hbox:
            spacing 10

            default bonus_button = None
            button:
                background None
                padding (0, 0)

                action ShowMenu("bonus")
                hovered SetLocalVariable('bonus_button', 'HOVERED')
                unhovered SetLocalVariable('bonus_button', 'UNHOVERED')

                has fixed
                fit_first True

                image "assets/menu/images/bonus_button.png"
                image "assets/menu/images/bonus_button_h.png":
                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 alpha 1.0
                    elif bonus_button == 'UNHOVERED':
                        at transform:
                            easeout .5 alpha .0
                    else:
                        at transform:
                            alpha .0

                vbox:
                    xalign 0.5
                    yalign 0.5

                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#ffffff")
                    else:
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#bdbdbd")
                    hbox:
                        spacing 5
                        add "assets/menu/images/bonus_icon.png"
                        text _("BONUS CONTENT") size 14 font "fonts/Montserrat-Medium.ttf"

            button at button_alpha:
                background "assets/menu/images/atch_button.png"
                xysize (48, 48)
                action ShowMenu("achievements")

    use navigation()
