screen block:
    add "assets/ui/location/FrontierBlock/background1.png" at parallax_bg
    use top()

    #location lock
    button at hover_effect:
        xpos 309
        ypos 553
        background None
        action Return()  # Replace with the action you want on click
        imagemap:
            idle "gui/block/lock_bg.png"
            add "gui/block/location_icon.png" xpos 10 ypos 90
            text _("Frontier Block") size 12 font "fonts/Montserrat-SemiBold.ttf" xpos 25 ypos 89 color "#C2C2C2"
            imagemap:
                idle "gui/block/box.png"
                xpos 0.5 xanchor 0.5
                ypos 120
                add "gui/block/lock_icon.png" yanchor 0.5 ypos 0.5 xanchor 0.5 xpos 0.5

    #location unlock
    button at hover_effect:
        xpos 816
        ypos 301
        background None
        action Return()  # Replace with the action you want on click
        imagemap:
            idle "gui/block/unlock_bg.png"
            add "gui/block/location_icon.png" xpos 10 ypos 90
            text _("Triad Block") size 12 font "fonts/Montserrat-SemiBold.ttf" xpos 25 ypos 89 color "#C2C2C2"
            imagemap:
                idle "gui/block/box.png"
                xpos 0.5 xanchor 0.5
                ypos 120
                add "gui/block/location_icon.png" yanchor 0.5 ypos 0.5 xanchor 0.5 xpos 0.5
    
    #location lock
    button at hover_effect:
        xpos 1203
        ypos 605
        background None
        action Return()  # Replace with the action you want on click
        imagemap:
            idle "gui/block/lock_bg.png"
            add "gui/block/location_icon.png" xpos 10 ypos 90
            text _("Voiceless Block") size 12 font "fonts/Montserrat-SemiBold.ttf" xpos 25 ypos 89 color "#C2C2C2"
            imagemap:
                idle "gui/block/box.png"
                xpos 0.5 xanchor 0.5
                ypos 120
                add "gui/block/lock_icon.png" yanchor 0.5 ypos 0.5 xanchor 0.5 xpos 0.5

    #Go back
    button at hover_effect:
        background "assets/ui/images/raid.png"
        add "assets/ui/images/raid_dots.png" align (0.5, 0.5)
        xysize (146, 146)
        xpos 0.45 ypos 0.6
        action Null()
        vbox:
            align (0.5, 0.5)
            spacing 10
            add "assets/ui/images/raid_up.png" xalign 0.5 
            text _("Go Back") size 14 font "fonts/Montserrat-SemiBold.ttf" yalign 0.5 color "#C2C2C2"
        at transform:
            on idle:
                ease 0.2 alpha 1.0
            on hover:
                ease 0.2 alpha 0.72
