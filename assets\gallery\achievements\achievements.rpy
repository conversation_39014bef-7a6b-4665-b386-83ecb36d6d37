# Определяем трансформации
transform achievements_appear:
    alpha 0.0
    yoffset 50
    pause 0.2
    parallel:
        ease 0.7 yoffset 0
    parallel:
        ease 0.7 alpha 1.0

transform achievements_hover_effect:
    on idle:
        ease 0.3 alpha 1.0
    on hover:
        ease 0.3 alpha 0.8

transform hover_with_ease:
    on idle:
        ease 0.3 alpha 1.0
    on hover:
        ease 0.3 alpha 0.8

# Экран ачивок
screen achievements():
    tag menu

    add "black"

    add "assets/gallery/images/main/background.png" ypos 138

    frame:
        background "assets/settings/images/logo.png" 
        xpos 853 ypos 40
        xysize (214, 65)
        text _("ACHIEVEMENTS") xalign 0.5 size 32 font "fonts/Montserrat-Black.ttf" color "#ffffff"

    # Добавляем подзаголовок Achievements с цветом #20ADA3
    frame:
        background None
        xpos 853 ypos 105
        xysize (214, 30)

    add "assets/settings/images/top_line.png" xpos 55 ypos 138

    # Верхнее меню
    hbox:
        xpos 55 ypos 59
        spacing 30
    
    
    # Добавляем баннер ачивок
    add "assets/gallery/achievements/images/banner.png" xpos 1398 ypos 179 xysize (466, 719)

    # Добавляем список ачивок
    frame at achievements_appear:
        background None
        xpos 55 ypos 179  # Позиция поднята на место кнопки Return
        xysize (1306, 700)  # Увеличиваем высоту, так как теперь больше места
        
        viewport:
            mousewheel True
            draggable True
            scrollbars None  # Скрываем полосу прокрутки, но оставляем функциональность
            xysize (1306, 700)
            
            vbox:
                spacing 20  # Вертикальный отступ между ачивками
                
                # Первая ачивка (разблокированная)
                frame:
                    background "assets/gallery/achievements/images/frame-unlock.png"
                    xysize (1306, 103)
                    
                    hbox:
                        spacing 20
                        xpos 20
                        yalign 0.5
                        
                        add "assets/gallery/achievements/images/star-unlock.png" yalign 0.5 xysize (60, 60)
                        
                        vbox:
                            yalign 0.5
                            spacing 5
                            
                            text _("First Steps") size 22 font "fonts/Montserrat-Bold.ttf" color "#FFFFFF"
                            text _("Complete the prologue") size 16 font "fonts/Montserrat-Medium.ttf" color "#BDBDBD"
                
                # Вторая ачивка (разблокированная)
                frame:
                    background "assets/gallery/achievements/images/frame-unlock.png"
                    xysize (1306, 103)
                    
                    hbox:
                        spacing 20
                        xpos 20
                        yalign 0.5
                        
                        add "assets/gallery/achievements/images/star-unlock.png" yalign 0.5 xysize (60, 60)
                        
                        vbox:
                            yalign 0.5
                            spacing 5
                            
                            text _("Explorer") size 22 font "fonts/Montserrat-Bold.ttf" color "#FFFFFF"
                            text _("Visit all locations in Chapter 1") size 16 font "fonts/Montserrat-Medium.ttf" color "#BDBDBD"
                
                # Третья ачивка (разблокированная)
                frame:
                    background "assets/gallery/achievements/images/frame-unlock.png"
                    xysize (1306, 103)
                    
                    hbox:
                        spacing 20
                        xpos 20
                        yalign 0.5
                        
                        add "assets/gallery/achievements/images/star-unlock.png" yalign 0.5 xysize (60, 60)
                        
                        vbox:
                            yalign 0.5
                            spacing 5
                            
                            text _("Curious Mind") size 22 font "fonts/Montserrat-Bold.ttf" color "#FFFFFF"
                            text _("Ask all available questions in a conversation") size 16 font "fonts/Montserrat-Medium.ttf" color "#BDBDBD"
                
                # Остальные ачивки (заблокированные)
                for i in range(12):
                    frame:
                        background "assets/gallery/achievements/images/frame-lock.png"
                        xysize (1306, 103)
                        
                        hbox:
                            spacing 20
                            xpos 20
                            yalign 0.5
                            
                            add "assets/gallery/achievements/images/star-lock.png" yalign 0.5 xysize (60, 60)
                            
                            vbox:
                                yalign 0.5
                                spacing 5
                                
                                text _("???") size 22 font "fonts/Montserrat-Bold.ttf" color "#8D8D8D"
                                text _("This achievement is locked") size 16 font "fonts/Montserrat-Medium.ttf" color "#8D8D8D"

    frame at slide_from_left:
        background "assets/menu/images/report_bg.svg"
        xysize (354, 57)
        xpos 55 ypos 40

        add "assets/menu/images/report_icon.svg" xpos 18 yalign 0.5
        text _("Did you find a bug?") xpos 47 yalign 0.5 size 16 font "fonts/Montserrat-Medium.ttf"

        button at button_alpha: 
            background 'assets/menu/images/report_button.svg'
            xysize (90, 34)
            xpos 246 yalign 0.5
            text _("Report") color "#000000" size 16 align (0.5, 0.5) font "fonts/Montserrat-SemiBold.ttf"
            action Start("")

    frame at slide_from_right:
        background None
        xysize (232, 48)
        xpos 1620 ypos 40

        hbox:
            spacing 10

            default achievements_button = None
            button:
                background None
                padding (0, 0)

                action Quit()
                hovered SetLocalVariable('achievements_button', 'HOVERED')
                unhovered SetLocalVariable('achievements_button', 'UNHOVERED')

                has fixed
                fit_first True

                image "assets/menu/images/bonus_button.png"
                image "assets/menu/images/bonus_button_h.png":
                    if achievements_button == 'HOVERED':
                        at transform:
                            easeout .5 alpha 1.0
                    elif achievements_button == 'UNHOVERED':
                        at transform:
                            easeout .5 alpha .0
                    else:
                        at transform:
                            alpha .0

                vbox:
                    xalign 0.5
                    yalign 0.5

                    if achievements_button == 'HOVERED':
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#ffffff")
                    else:
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#bdbdbd")
                    hbox:
                        spacing 5
                        add "assets/menu/images/bonus_icon.png"
                        text _("BONUS CONTENT") size 14 font "fonts/Montserrat-Medium.ttf"

            button at button_alpha:
                background "assets/menu/images/atch_button.png"
                xysize (48, 48)
                action ShowMenu("achievements")

    use navigation()
