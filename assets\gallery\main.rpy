
init python:

    pass


transform gallery_button_appear(delay):
    alpha 0.0
    yoffset 50
    pause delay
    parallel:
        ease 0.7 yoffset 0
    parallel:
        ease 0.7 alpha 1.0

transform hover_effect:
    on idle:
        ease 0.3 alpha 1.0
    on hover:
        ease 0.3 alpha 0.8

transform gallery_button_hover:
    on idle:
        ease 0.5 alpha 1.0
    on hover:
        ease 0.5 alpha 0.8


screen main():
    tag menu

    add "black"

    add "assets/gallery/images/main/background.png" ypos 138

    frame:
        background "assets/settings/images/logo.png" 
        xpos 853 ypos 40
        xysize (214, 65)
        text _("GALLERY") align (0.5, 0.5) size 16 font "fonts/Montserrat-Black.ttf"

    add "assets/settings/images/top_line.png" xpos 55 ypos 138

    frame at gallery_button_appear(0.2):
        background None
        xpos 55 ypos 178
        xysize (649, 716)
        
        imagebutton at hover_effect:
            idle "assets/gallery/images/main/replay.png"
            hover "assets/gallery/images/main/replay.png"
            action ShowMenu("replay")
            xsize 649 ysize 716
        
        vbox:
            xpos 20
            yalign 0.95
            spacing 15
            
            hbox:
                spacing 10
                add "assets/gallery/images/main/replay_icon.png" yalign 0.5
                text _("Replay Scene") size 20 font "fonts/Montserrat-Bold.ttf" yalign 0.5
            
            text _("10/15") size 14 font "fonts/Montserrat-Medium.ttf" xpos 5 color "#9FA0A3"

    # Добавляем кнопку Bonus
    frame at gallery_button_appear(0.4):
        background None
        xpos 741 ypos 178
        xysize (649, 716)
        
        imagebutton at hover_effect:
            idle "assets/gallery/images/main/bonus.png"
            hover "assets/gallery/images/main/bonus.png"
            action ShowMenu("bonus")
            xsize 649 ysize 716
        
        vbox:
            xpos 20
            yalign 0.95
            spacing 15
            
            hbox:
                spacing 10
                add "assets/gallery/images/main/bonus_icon.png" yalign 0.5
                text _("Bonus Content") size 20 font "fonts/Montserrat-Bold.ttf" yalign 0.5
            
            text _("10/20") size 14 font "fonts/Montserrat-Medium.ttf" xpos 5 color "#9FA0A3"

    # Добавляем кнопку Codex & Relationship
    frame at gallery_button_appear(0.6):
        background None
        xpos 1435 ypos 178
        xysize (429, 716)
        
        imagebutton at hover_effect:
            idle "assets/gallery/images/main/codex.png"
            hover "assets/gallery/images/main/codex.png"
            action ShowMenu("codex")
            xsize 429 ysize 716
        
        vbox:
            xpos 20
            yalign 0.95
            spacing 15
            
            hbox:
                spacing 10
                add "assets/gallery/images/main/codex-icon.png" yalign 0.5
                text _("Codex & Relationship") size 20 font "fonts/Montserrat-Bold.ttf" yalign 0.5
            
            text _("10/20") size 14 font "fonts/Montserrat-Medium.ttf" xpos 5 color "#9FA0A3"

    frame at slide_from_left:
        background "assets/menu/images/report_bg.svg"
        xysize (354, 57)
        xpos 55 ypos 40

        add "assets/menu/images/report_icon.svg" xpos 18 yalign 0.5
        text _("Did you find a bug?") xpos 47 yalign 0.5 size 16 font "fonts/Montserrat-Medium.ttf"

        button at button_alpha: 
            background 'assets/menu/images/report_button.svg'
            xysize (90, 34)
            xpos 246 yalign 0.5
            text _("Report") color "#000000" size 16 align (0.5, 0.5) font "fonts/Montserrat-SemiBold.ttf"
            action Start("")

    frame at slide_from_right:
        background None
        xysize (232, 48)
        xpos 1620 ypos 40

        hbox:
            spacing 10



            default bonus_button = None
            button:
                background None
                padding (0, 0)

                action ShowMenu("bonus")
                hovered SetLocalVariable('bonus_button', 'HOVERED')
                unhovered SetLocalVariable('bonus_button', 'UNHOVERED')

                has fixed
                fit_first True

                image "assets/menu/images/bonus_button.png"
                image "assets/menu/images/bonus_button_h.png":
                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 alpha 1.0
                    elif bonus_button == 'UNHOVERED':
                        at transform:
                            easeout .5 alpha .0
                    else:
                        at transform:
                            alpha .0

                vbox:
                    xalign 0.5
                    yalign 0.5

                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#ffffff")
                    else:
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#bdbdbd")
                    hbox:
                        spacing 5
                        add "assets/menu/images/bonus_icon.png"
                        text _("BONUS CONTENT") size 14 font "fonts/Montserrat-Medium.ttf"


            button at button_alpha:
                background "assets/menu/images/atch_button.png"
                xysize (48, 48)
                action ShowMenu("achievements")



    use navigation()