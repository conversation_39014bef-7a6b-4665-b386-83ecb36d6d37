init python:
    # Font definitions
    style.default.font = "fonts/Montserrat-Medium.ttf"
    style.button_text.font = "fonts/Montserrat-Medium.ttf"
    
    def parallax_effect(trans, st, at):
        x, y = renpy.get_mouse_pos()
        offset_x = (x / config.screen_width - 0.5) * 40
        offset_y = (y / config.screen_height - 0.5) * 40
        trans.xoffset = offset_x
        trans.yoffset = offset_y
        return 0.0
    
transform parallax_bg:
    subpixel True
    zoom 1.05
    xalign 0.5
    yalign 0.5
    xoffset 0 yoffset 0
    function parallax_effect

transform button_alpha:
    on idle:
        ease 0.5 alpha 1.0
    on hover:
        ease 0.5 alpha 0.5

transform slide_from_left:
    xpos -500
    easein 1.0 xpos 55

transform slide_from_right:
    xpos 2000
    easein 1.0 xpos 1620

transform slide_from_bottom(delay=0.0):
    yoffset 1000  # Using offset instead of absolute position
    pause delay  # Add delay before animation starts
    easein 1.2 yoffset 0  # Animate to original position

transform logo_appear:
    xpos 780 ypos 587
    ease 1.8 zoom 1.0 alpha 1.0  # Scale up and fade in
    on hover:
        ease 0.3 zoom 1.05
    on idle:
        ease 0.3 zoom 1.0

transform bg_show:
    alpha 0.0
    easein 0.5 alpha 1.0

transform bg_hide:
    alpha 1.0
    easeout 0.5 alpha 0.0

transform dissolve_bg:
    on show:
        alpha 0.0
        easein 0.5 alpha 1.0
    on hide:
        easeout 0.5 alpha 0.0

screen main_menu():

    tag menu

    default current_bg = "df"  # Track current background
    
    # Base background - always visible
    add "assets/menu/images/bg/df_bg.png" at parallax_bg

    # Overlay backgrounds with transforms
    showif current_bg == "newgame":
        add "assets/menu/images/bg/newgame.png" at [parallax_bg, dissolve_bg]
    
    showif current_bg == "load":
        add "assets/menu/images/bg/load.png" at [parallax_bg, dissolve_bg]
    
    showif current_bg == "settings":
        add "assets/menu/images/bg/settings.png" at [parallax_bg, dissolve_bg]

    # Add a transparent button for mouse tracking
    button:
        background "#0000"
        xysize (config.screen_width, config.screen_height)
        hovered NullAction()

    # Top section

    frame at slide_from_left:
        background "assets/menu/images/report_bg.svg"
        xysize (354, 57)
        xpos 55 ypos 40

        add "assets/menu/images/report_icon.svg" xpos 18 yalign 0.5
        text _("Did you find a bug?") xpos 47 yalign 0.5 size 16 font "fonts/Montserrat-Medium.ttf"

        button at button_alpha: 
            background 'assets/menu/images/report_button.svg'
            xysize (90, 34)
            xpos 246 yalign 0.5
            text _("Report") color "#000000" size 16 align (0.5, 0.5) font "fonts/Montserrat-SemiBold.ttf"
            action Start("")

    frame at slide_from_right:
        background None
        xysize (232, 48)
        xpos 1620 ypos 40

        hbox:
            spacing 10



            default bonus_button = None
            button:
                background None
                padding (0, 0)

                action ShowMenu("bonus")
                hovered SetLocalVariable('bonus_button', 'HOVERED')
                unhovered SetLocalVariable('bonus_button', 'UNHOVERED')

                has fixed
                fit_first True

                image "assets/menu/images/bonus_button.png"
                image "assets/menu/images/bonus_button_h.png":
                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 alpha 1.0
                    elif bonus_button == 'UNHOVERED':
                        at transform:
                            easeout .5 alpha .0
                    else:
                        at transform:
                            alpha .0

                vbox:
                    xalign 0.5
                    yalign 0.5

                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#ffffff")
                    else:
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#bdbdbd")
                    hbox:
                        spacing 5
                        add "assets/menu/images/bonus_icon.png"
                        text _("BONUS CONTENT") size 14 font "fonts/Montserrat-Medium.ttf"


            button at button_alpha:
                background "assets/menu/images/atch_button.png"
                xysize (48, 48)
                action ShowMenu("achievements")


    # End

    # Middle section

    # Left Middle section
    frame at slide_from_bottom(0.0):  # Start immediately
        background None
        xysize (258, 45)
        xpos 55 ypos 712
        
        hbox:
            spacing 10

            button at button_alpha:
                background "assets/menu/images/info_version.svg"
                xysize (122, 45)
                hbox:
                    align (0.5, 0.5)
                    spacing 5
                    add "assets/menu/images/fire_icon.png"
                    text _("V 1.0") size 16 yalign 0.5 color "#000000" font "fonts/Montserrat-Medium.ttf"
                action Start("")

            button at button_alpha:
                background "assets/menu/images/gallery_button.png"
                xysize (122, 45)
                hbox:
                    align (0.5, 0.5)
                    spacing 5
                    add "assets/menu/images/gallery_icon.png"
                    text _("Gallery") size 16 yalign 0.5 color "#c6c6c6" font "fonts/Montserrat-Medium.ttf"
                action ShowMenu("main")

    # Center Middle section
    add "assets/menu/images/logo.png":
        xpos 780 ypos 587  # Set base position
        at slide_from_bottom(0.0)  # Start immediately

    add "assets/menu/images/line.png" xpos 55 ypos 789


    # Right Middle section
    frame at slide_from_bottom(0.0):  # Start immediately
        background None
        xysize (203, 44)
        xpos 1640 ypos 700
        
        hbox:
            spacing 15
            
            button at button_alpha:
                xysize (44, 44)
                background "assets/menu/images/social/patreon.png"
                action OpenURL("https://patreon.com")
                
            button at button_alpha:
                xysize (44, 44)
                background "assets/menu/images/social/discord.png"
                action OpenURL("https://discord.gg")
                
            button at button_alpha:
                xysize (44, 44)
                background "assets/menu/images/social/x.png"
                action OpenURL("https://twitter.com")
                
            button at button_alpha:
                xysize (44, 44)
                background "assets/menu/images/social/steam.png"
                action OpenURL("https://store.steampowered.com")


    # Bottom section
    frame at slide_from_bottom(0.8):  # Start after 0.8 seconds delay
        background None
        xysize (1808, 224)
        xalign 0.5 ypos 820
        
        hbox:
            spacing 25
            
            # New Game Button
            default newgame_button = None
            button:
                background None
                padding (0, 0)
                action Start()
                hovered [SetLocalVariable('newgame_button', 'HOVERED'), SetScreenVariable('current_bg', 'newgame')]
                unhovered [SetLocalVariable('newgame_button', 'UNHOVERED'), SetScreenVariable('current_bg', 'df')]

                has fixed:
                    fit_first True
                    xysize (639, 224)

                image "assets/menu/images/button/start_game.png"
                image "assets/menu/images/button/start_game_h.png":
                    if newgame_button == 'HOVERED':
                        at transform:
                            easeout .5 alpha 1.0
                    elif newgame_button == 'UNHOVERED':
                        at transform:
                            easeout .5 alpha .0
                    else:
                        at transform:
                            alpha .0

                vbox:
                    xalign 0.08 yalign 0.85
                    spacing 3  
                    text _("NEW GAME"):
                        size 28  
                        color "#ffffff"
                        font "fonts/Montserrat-Black.ttf"
                    text _("Click to start a new game"):
                        size 14  
                        color "#9FA0A3"
                        font "fonts/Montserrat-Medium.ttf"

            # Load Button
            default load_button = None
            button:
                background None
                padding (0, 0)
                action ShowMenu("load")
                hovered [SetLocalVariable('load_button', 'HOVERED'), SetScreenVariable('current_bg', 'load')]
                unhovered [SetLocalVariable('load_button', 'UNHOVERED'), SetScreenVariable('current_bg', 'df')]

                has fixed:
                    fit_first True
                    xysize (498, 224)

                image "assets/menu/images/button/load.png"
                image "assets/menu/images/button/load_h.png":
                    if load_button == 'HOVERED':
                        at transform:
                            easeout .5 alpha 1.0
                    elif load_button == 'UNHOVERED':
                        at transform:
                            easeout .5 alpha .0
                    else:
                        at transform:
                            alpha .0

                vbox:
                    xalign 0.08 yalign 0.85
                    spacing 3  
                    text _("LOAD"):
                        size 28  
                        color "#ffffff"
                        font "fonts/Montserrat-Black.ttf"
                    text _("Load the game"):
                        size 14  
                        color "#9FA0A3"
                        font "fonts/Montserrat-Medium.ttf"

            # Settings Button
            default settings_button = None
            button:
                background None
                padding (0, 0)
                action ShowMenu("preferences")
                hovered [SetLocalVariable('settings_button', 'HOVERED'), SetScreenVariable('current_bg', 'settings')]
                unhovered [SetLocalVariable('settings_button', 'UNHOVERED'), SetScreenVariable('current_bg', 'df')]

                has fixed:
                    fit_first True
                    xysize (498, 224)

                image "assets/menu/images/button/settings.png"
                image "assets/menu/images/button/settings_h.png":
                    if settings_button == 'HOVERED':
                        at transform:
                            easeout .5 alpha 1.0
                    elif settings_button == 'UNHOVERED':
                        at transform:
                            easeout .5 alpha .0
                    else:
                        at transform:
                            alpha .0

                vbox:
                    xalign 0.15 yalign 0.85
                    spacing 3  
                    text _("SETTINGS"):
                        size 28  
                        color "#ffffff"
                        font "fonts/Montserrat-Black.ttf"
                    text _("Customize the game to your taste"):
                        size 14  
                        color "#9FA0A3"
                        font "fonts/Montserrat-Medium.ttf"

            # Quit Button
            default quit_button = None
            button:
                background None
                padding (0, 0)
                action Quit()
                hovered SetLocalVariable('quit_button', 'HOVERED')
                unhovered SetLocalVariable('quit_button', 'UNHOVERED')

                has fixed:
                    fit_first True
                    xysize (77, 224)

                image "assets/menu/images/button/quit.png"
                image "assets/menu/images/button/quit_h.png":
                    if quit_button == 'HOVERED':
                        at transform:
                            easeout .5 alpha 1.0
                    elif quit_button == 'UNHOVERED':
                        at transform:
                            easeout .5 alpha .0
                    else:
                        at transform:
                            alpha .0
