init python:
    # Ensure persistent storage is initialized
    if not hasattr(persistent, '_current_save_page'):
        persistent._current_save_page = '1'
    
    max_pages = 500

    class SlotStates(object):
        def __init__(self):
            self.hover_states = {}
        
        def set_hover(self, slot, state):
            self.hover_states[slot] = state
        
        def get_hover(self, slot):
            return self.hover_states.get(slot, None)
        
        def clear_all(self):
            self.hover_states.clear()
    
    slot_states = SlotStates()

transform sizeslot:
    subpixel True
    on idle:
        easeout .3 alpha 1
    on hover:
        easeout .3 alpha 0.5

transform sizes:
    subpixel True
    on idle:
        easeout .3 zoom 1
    on hover:
        easeout .3 alpha 1.05

# Добавляем новые трансформы для анимации
transform slot_appear(delay, i):
    # i используется для последовательного появления слотов
    alpha 0.0
    yoffset 50
    pause delay + i * 0.1  # Добавляем задержку для каждого слота
    parallel:
        ease 0.5 yoffset 0
    parallel:
        ease 0.5 alpha 1.0

transform fade_up(delay):
    yoffset 50
    alpha 0.0
    pause delay
    parallel:
        ease 0.5 yoffset 0
    parallel:
        ease 0.5 alpha 1.0

transform pages_appear(delay):
    alpha 0.0
    xoffset -50
    pause delay
    parallel:
        ease 0.5 xoffset 0
    parallel:
        ease 0.5 alpha 1.0

# Обновляем эффект наведения для слотов
transform slot_hover:
    on idle:
        parallel:
            ease 0.7 zoom 1.0  # Увеличиваем время анимации
            ease 0.7 alpha 1.0
        parallel:
            ease 0.7 yoffset 0
    on hover:
        parallel:
            ease 0.7 zoom 1.02
            ease 0.7 alpha 0.9
        parallel:
            ease 0.7 yoffset -5

transform slot_highlight:
    alpha 0.0
    on hover:
        ease 0.5 alpha 0.3
    on idle:
        ease 0.5 alpha 0.0

# Обновляем эффект наведения для слотов сохранения в стиле меню
transform slot_hover:
    on idle:
        parallel:
            ease 0.7 zoom 1.0
            ease 0.7 alpha 1.0
        parallel:
            ease 0.7 yoffset 0
            ease 0.7 matrixcolor ColorizeMatrix("#000", "#d5d5d5")
    on hover:
        parallel:
            ease 0.7 zoom 1.02
            ease 0.7 alpha 0.95
        parallel:
            ease 0.7 yoffset -5
            ease 0.7 matrixcolor ColorizeMatrix("#000", "#111111")

# Добавляем новый трансформ для кнопок перелистывания
transform arrow_button:
    on idle:
        ease 0.3 alpha 0.7
    on hover:
        ease 0.3 alpha 1.0



screen save():

    tag menu

    add "black"

    frame:
        background "assets/settings/images/logo.png" 
        xpos 853 ypos 40
        xysize (214, 65)
        text _("SAVE") align (0.5, 0.5) size 16 font "fonts/Montserrat-Black.ttf"

    add "assets/settings/images/top_line.png" xpos 55 ypos 138

    frame at slide_from_left:
        background "assets/menu/images/report_bg.svg"
        xysize (354, 57)
        xpos 55 ypos 40

        add "assets/menu/images/report_icon.svg" xpos 18 yalign 0.5
        text _("Did you find a bug?") xpos 47 yalign 0.5 size 16 font "fonts/Montserrat-Medium.ttf"

        button at button_alpha: 
            background 'assets/menu/images/report_button.svg'
            xysize (90, 34)
            xpos 246 yalign 0.5
            text _("Report") color "#000000" size 16 align (0.5, 0.5) font "fonts/Montserrat-SemiBold.ttf"
            action Start("")

    frame at slide_from_right:
        background None
        xysize (232, 48)
        xpos 1620 ypos 40

        hbox:
            spacing 10

            default bonus_button = None
            button:
                background None
                padding (0, 0)

                action ShowMenu("bonus")
                hovered SetLocalVariable('bonus_button', 'HOVERED')
                unhovered SetLocalVariable('bonus_button', 'UNHOVERED')

                has fixed
                fit_first True

                image "assets/menu/images/bonus_button.png"
                image "assets/menu/images/bonus_button_h.png":
                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 alpha 1.0
                    elif bonus_button == 'UNHOVERED':
                        at transform:
                            easeout .5 alpha .0
                    else:
                        at transform:
                            alpha .0

                vbox:
                    xalign 0.5
                    yalign 0.5

                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#ffffff")
                    else:
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#bdbdbd")
                    hbox:
                        spacing 5
                        add "assets/menu/images/bonus_icon.png"
                        text _("BONUS CONTENT") size 14 font "fonts/Montserrat-Medium.ttf"


            button at button_alpha:
                background "assets/menu/images/atch_button.png"
                xysize (48, 48)
                action ShowMenu("achievements")


    add "assets/save-load/images/banner-save.png" xpos 48 ypos 179


    use file_slots(_(""))

screen load():

    tag menu

    # Top section

    add "black"

    frame:
        background "assets/settings/images/logo.png" 
        xpos 853 ypos 40
        xysize (214, 65)
        text _("LOAD") align (0.5, 0.5) size 16 font "fonts/Montserrat-Black.ttf"

    add "assets/settings/images/top_line.png" xpos 55 ypos 138

    frame at slide_from_left:
        background "assets/menu/images/report_bg.svg"
        xysize (354, 57)
        xpos 55 ypos 40

        add "assets/menu/images/report_icon.svg" xpos 18 yalign 0.5
        text _("Did you find a bug?") xpos 47 yalign 0.5 size 16 font "fonts/Montserrat-Medium.ttf"

        button at button_alpha: 
            background 'assets/menu/images/report_button.svg'
            xysize (90, 34)
            xpos 246 yalign 0.5
            text _("Report") color "#000000" size 16 align (0.5, 0.5) font "fonts/Montserrat-SemiBold.ttf"
            action Start("")

    frame at slide_from_right:
        background None
        xysize (232, 48)
        xpos 1620 ypos 40

        hbox:
            spacing 10

            default bonus_button = None
            button:
                background None
                padding (0, 0)

                action ShowMenu("bonus")
                hovered SetLocalVariable('bonus_button', 'HOVERED')
                unhovered SetLocalVariable('bonus_button', 'UNHOVERED')

                has fixed
                fit_first True

                image "assets/menu/images/bonus_button.png"
                image "assets/menu/images/bonus_button_h.png":
                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 alpha 1.0
                    elif bonus_button == 'UNHOVERED':
                        at transform:
                            easeout .5 alpha .0
                    else:
                        at transform:
                            alpha .0

                vbox:
                    xalign 0.5
                    yalign 0.5

                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#ffffff")
                    else:
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#bdbdbd")
                    hbox:
                        spacing 5
                        add "assets/menu/images/bonus_icon.png"
                        text _("BONUS CONTENT") size 14 font "fonts/Montserrat-Medium.ttf"


            button at button_alpha:
                background "assets/menu/images/atch_button.png"
                xysize (48, 48)
                action ShowMenu("achievements")


    add "assets/save-load/images/banner-load.png" xpos 48 ypos 179

    use file_slots(_(""))

screen file_slots(title):

    on "hide" action Function(slot_states.clear_all)  # Only keep the hide action
    
    $ current_page = FileCurrentPage()  # Get current page at start of screen
    
    grid 3 2:
        style_prefix "slot"

        align (.94, .36)    
        spacing 30

        for i in range(gui.file_slot_cols * gui.file_slot_rows):

            $ slot = i + 1

            button:
                at slot_appear(0.2, i)
                background None
                padding (0,0)
                action FileAction(slot)
                hovered Function(slot_states.set_hover, slot, 'HOVERED')
                unhovered Function(slot_states.set_hover, slot, 'UNHOVERED')
                xysize (427, 269)
                
                has fixed
                fit_first True
                
                # Основной слот
                image "assets/save-load/images/slot.png"
                
                if FileScreenshot(slot):
                    # Скриншот с маской как у слота
                    add AlphaMask(Transform(FileScreenshot(slot), xsize=427, ysize=269), "assets/save-load/images/mask.png"):
                        xsize 427 ysize 269
                
                # Время и дата сохранения
                if FileTime(slot):
                    vbox:
                        xalign 0.0
                        yalign 1.0
                        xoffset 20
                        yoffset -15
                        text FileTime(slot, format="%d.%m.%Y"):
                            size 16
                            color "#ffffffbb"
                            font "fonts/Montserrat-Medium.ttf"
                        text FileTime(slot, format="%H:%M"):
                            size 24
                            color "#ffffff"
                            font "fonts/Montserrat-Bold.ttf"
                
                # Эффект при наведении
                image "assets/save-load/images/slot1.png":
                    if slot_states.get_hover(slot) == 'HOVERED':
                        at transform:
                            easeout .5 alpha 1.0
                    else:
                        at transform:
                            easeout .5 alpha 0.0
                
                key "save_delete" action FileDelete(slot)

    default page_name_value = FilePageNameInputValue(pattern=_("Page {}"), auto=_("Automatic Saves"), quick=_("Quick Saves"))

    add "assets/save-load/images/line.png" xpos 545 ypos 800


    use game_menu(title):

        fixed:

            order_reverse True
            button at fade_up(0.3):
                key_events True
                align (0.05, 0.80)
                action page_name_value.Toggle()

                input:
                    style "page_label_text"
                    size 30
                    font "fonts/Montserrat-SemiBold.ttf"
                    color (137,137,137)
                    value page_name_value

            vbox at pages_appear(0.4):
                style_prefix "page"
                align (0.98, 0.80)

                hbox:
                    spacing 40
                    
                    # Auto and Quick saves group
                    hbox:
                        spacing 10
                        if config.has_autosave:
                            $ is_auto = str(current_page) == "auto"
                            $ auto_text_color = (0, 0, 0) if is_auto else (140, 140, 140)
                            $ auto_bg = "gui/page-a-a.svg" if is_auto else "gui/page-a.svg"
                            button at sizeslot:
                                text _("{#auto_page}A"):
                                    align (0.5, 0.5)
                                    size 20
                                    font "fonts/Montserrat-SemiBold.ttf"
                                    color auto_text_color
                                xysize (73, 42)
                                background auto_bg
                                action [FilePage("auto"), With(dissolve)]

                        if config.has_quicksave:
                            $ is_quick = str(current_page) == "quick"
                            $ quick_text_color = (0, 0, 0) if is_quick else (140, 140, 140)
                            $ quick_bg = "gui/page-a-a.svg" if is_quick else "gui/page-a.svg"
                            button:
                                text _("{#quick_page}Q"):
                                    align (0.5, 0.5)
                                    size 20
                                    font "fonts/Montserrat-SemiBold.ttf"
                                    color quick_text_color
                                xysize (73, 42)
                                background quick_bg
                                action [FilePage("quick"), With(dissolve)]


                    # Navigation pages group
                    hbox:
                        spacing 20
                        
                        button:
                            at arrow_button
                            xysize (42, 42)
                            background "gui/arrow_left.png"
                            action [FilePagePrevious(), With(dissolve)]
                        
                        hbox:
                            spacing 10
                            $ current_str = str(current_page)
                            $ current_num = int(current_str) if current_str.isdigit() else 1
                            $ start_page = ((current_num - 1) // 10) * 10 + 1
                            $ end_page = min(start_page + 9, max_pages)

                            for page in range(start_page, end_page + 1):
                                $ is_current = str(current_page) == str(page)
                                $ page_text_color = (0, 0, 0) if is_current else (140, 140, 140)
                                $ page_bg = "gui/page-list-a.svg" if is_current else "gui/page-list.svg"
                                button at sizeslot:
                                    xysize (42, 42)
                                    background page_bg
                                    text "[page]":
                                        align (0.5, 0.5)
                                        size 20
                                        font "fonts/Montserrat-SemiBold.ttf"
                                        color page_text_color
                                    action [FilePage(page), With(dissolve)]
                        
                        button:
                            at arrow_button
                            xysize (42, 42)
                            background "gui/arrow_right.png"
                            action [FilePageNext(), With(dissolve)]