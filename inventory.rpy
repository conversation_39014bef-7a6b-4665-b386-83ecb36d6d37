init python:
    debug_visible = False  # Tracks if debug screen is visible
init python:
    # Player health for debug/demo
    player_health = 100

    # Inventory items list
    inventory_items = [
        {"name": "Health Potion", "image": "gui/inventory/item_idle.png", "available": True},
        {"name": "<PERSON><PERSON> Potion", "image": "gui/inventory/item_idle.png", "available": True},
        {"name": "Elixir", "image": "gui/inventory/item_idle.png", "available": True},
        {"name": "Bomb", "image": "gui/inventory/item_idle.png", "available": True},
        {"name": "Key", "image": "gui/inventory/item_idle.png", "available": True},
        {"name": "Scroll", "image": "gui/inventory/item_idle.png", "available": True},
    ]

    def consume_item(index):
        """
        Consume the item at given index.
        Marks it unavailable and reduces player health for demo.
        """
        global player_health
        if 0 <= index < len(inventory_items) and inventory_items[index]["available"]:
            inventory_items[index]["available"] = False
            player_health = max(0, player_health - 10)  # example effect: reduce health by 10
            renpy.notify(f"Consumed {inventory_items[index]['name']}! Health is now {player_health}.")
        else:
            renpy.notify("Item not available or invalid index.")

    def add_item(name, image="gui/inventory/item_idle.png"):
        """
        Add a new item to inventory (for debug).
        """
        inventory_items.append({"name": name, "image": image, "available": True})
        renpy.notify(f"Added {name} to inventory.")

    def remove_item(index):
        """
        Remove item at index (for debug).
        """
        if 0 <= index < len(inventory_items):
            removed = inventory_items.pop(index)
            renpy.notify(f"Removed {removed['name']} from inventory.")
        else:
            renpy.notify("Invalid index for removal.")

screen confirm_consume(index):
    add "gui/inventory/confirm.png"
    modal True
    frame:
        xalign 0.5
        yalign 0.5
        padding (20, 20, 20, 20)
        background None
        vbox:
            xalign 0.5
            spacing 20
            text "Consume [inventory_items[index]['name']]?"font "fonts/Montserrat-SemiBold.ttf" size 30
            hbox:
                ypos 67
                spacing 180
                xalign 0.5
                textbutton "Yes":
                    action [Function(consume_item, index), Hide("confirm_consume"), Show("inventory")]
                textbutton "No":
                    action Hide("confirm_consume")

screen inventory():
    add "gui/inventory/background.png" xpos 0.5 ypos 0.5 xanchor 0.5 yanchor 0.5

    frame:
        background "assets/settings/images/logo.png"
        xpos 853 ypos 260
    frame:
        background "assets/settings/images/logo.png"
        xpos 853 ypos 260
        xysize (214, 65)
        text _("INVENTORY") align (0.5, 0.5) size 16 font "fonts/Montserrat-Black.ttf"

    button at button_alpha:
        background 'assets/menu/images/report_button.svg'
        xysize (90, 34)
        xpos 0.5 ypos 0.73 xanchor 0.5
        text _("CLOSE") color "#000000" size 16 align (0.5, 0.5) font "fonts/Montserrat-SemiBold.ttf"
        action Hide("inventory")

    #DEBUG
    button:
        background 'assets/menu/images/report_button.svg'
        xysize (90, 34)
        text _("debug") color "#000000" size 16 align (0.5, 0.5) font "fonts/Montserrat-SemiBold.ttf"
        action If(debug_visible,
                [Hide("debug_inventory"), SetVariable("debug_visible", False)],
                [Show("debug_inventory"), SetVariable("debug_visible", True)])


    frame:
        background None
        xpos 0.23 ypos 350
        xysize (1050, 400)

        viewport:
            mousewheel True
            draggable (False, True)
            scrollbars "vertical"
            xysize (1010, 650)

            grid 4 5:
                xspacing 15
                yspacing 15

                for i, item in enumerate(inventory_items):
                    if item["available"]:
                        frame:
                            background None
                            xysize (230, 157)
                            has vbox  # Stack imagebutton and text vertically
                            spacing 5
                            align (0.5, 0.5)

                            imagebutton at hover_with_ease:
                                idle item["image"]
                                hover "gui/inventory/item_hover.png"
                                xsize 230 ysize 130
                                action Show("confirm_consume", index=i)

                            text item["name"]:
                                size 14
                                color "#fff"
                                font "fonts/Montserrat-SemiBold.ttf"
                                align (0.5, 0.5)

screen debug_inventory():
    frame:
        xalign 0.95
        yalign 0.05
        background "#0008"
        padding (20, 20, 20, 20)
        vbox:
            spacing 10
            text "DEBUG MENU" size 18 color "#fff"
            text "Player Health: [player_health]" size 16

            textbutton "Add Health Potion":
                action Function(add_item, "Health Potion")

            textbutton "Add Mana Potion":
                action Function(add_item, "Mana Potion")

            textbutton "Remove Last Item":
                action Function(remove_item, len(inventory_items) - 1)

            textbutton "Heal Player +10":
                action [
                    SetVariable("player_health", min(100, player_health + 10)),
                    Function(renpy.notify, "Healed! Health is now [player_health].")
                ]

            textbutton "Damage Player -10":
                action [
                    SetVariable("player_health", max(0, player_health - 10)),
                    Function(renpy.notify, "Damaged! Health is now [player_health].")
                ]
