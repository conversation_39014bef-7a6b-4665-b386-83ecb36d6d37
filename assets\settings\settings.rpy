init python:
    # Define available languages
    renpy.available_languages = ["russian", "english"]
    
    def switch_language(lang):
        if lang == "russian":
            renpy.change_language("russian")
        else:
            renpy.change_language("english")

    def toggle_skip_setting(field):
        current = getattr(_preferences, field)
        setattr(_preferences, field, not current)
        if field == "skip_unseen":
            persistent.skip_unseen = not current

    if not hasattr(persistent, "mute_button_selected"):
        persistent.mute_button_selected = False

# Define transforms at module level
transform settings_frame_appear:
    on show:
        alpha 0.0
        easein 0.3 alpha 1.0
    on hide:
        ease 0.3 alpha 0.0

transform button_hover:
    on hover:
        ease 0.2 alpha 0.7
    on idle:
        ease 0.2 alpha 1.0

screen preferences():

    tag menu

    add "black"

    add "assets/settings/images/section_line.png" xpos 782 ypos 479
    add "assets/settings/images/section_line.png" xpos 782 ypos 601

    # Top section

    frame:
        background "assets/settings/images/logo.png" 
        xpos 853 ypos 40
        xysize (214, 65)
        text _("GAME SETTINGS") align (0.5, 0.5) size 16 font "fonts/Montserrat-Black.ttf"

    add "assets/settings/images/top_line.png" xpos 55 ypos 138

    add "assets/settings/images/left_image.png" xpos 55 ypos 179

    frame at slide_from_left:
        background "assets/menu/images/report_bg.svg"
        xysize (354, 57)
        xpos 55 ypos 40

        add "assets/menu/images/report_icon.svg" xpos 18 yalign 0.5
        text _("Did you find a bug?") xpos 47 yalign 0.5 size 16 font "fonts/Montserrat-Medium.ttf"

        button at button_alpha: 
            background 'assets/menu/images/report_button.svg'
            xysize (90, 34)
            xpos 246 yalign 0.5
            text _("Report") color "#000000" size 16 align (0.5, 0.5) font "fonts/Montserrat-SemiBold.ttf"
            action Start("")

    frame at slide_from_right:
        background None
        xysize (232, 48)
        xpos 1620 ypos 40

        hbox:
            spacing 10



            default bonus_button = None
            button:
                background None
                padding (0, 0)

                action ShowMenu("bonus")
                hovered SetLocalVariable('bonus_button', 'HOVERED')
                unhovered SetLocalVariable('bonus_button', 'UNHOVERED')

                has fixed
                fit_first True

                image "assets/menu/images/bonus_button.png"
                image "assets/menu/images/bonus_button_h.png":
                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 alpha 1.0
                    elif bonus_button == 'UNHOVERED':
                        at transform:
                            easeout .5 alpha .0
                    else:
                        at transform:
                            alpha .0

                vbox:
                    xalign 0.5
                    yalign 0.5

                    if bonus_button == 'HOVERED':
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#ffffff")
                    else:
                        at transform:
                            easeout .5 matrixcolor ColorizeMatrix("#000", "#bdbdbd")
                    hbox:
                        spacing 5
                        add "assets/menu/images/bonus_icon.png"
                        text _("BONUS CONTENT") size 14 font "fonts/Montserrat-Medium.ttf"


            button at button_alpha:
                background "assets/menu/images/atch_button.png"
                xysize (48, 48)
                action ShowMenu("achievements")

    # Screen settings

    frame at settings_frame_appear:
        background "assets/settings/images/screen_settings.png"
        xpos 782 ypos 179
        xysize (420, 80)

        # Title
        text _("Screen") xpos 20 yalign 0.5 size 16 font "fonts/Montserrat-SemiBold.ttf"

        # Window/Fullscreen Toggle
        hbox:
            xpos 200 yalign 0.5
            spacing 15

            # Window button
            button at button_hover:
                action Preference("display", "window")
                $ window_color = "#FFFFFF" if not preferences.fullscreen else "#9FA0A3"
                text _("Window"):
                    size 14
                    font "fonts/Montserrat-Medium.ttf"
                    color window_color

            # Separator line
            add "assets/settings/images/line.png":
                yalign 0.5
                xysize(2, 20)

            # Fullscreen button
            button at button_hover:
                action Preference("display", "fullscreen")
                $ fullscreen_color = "#FFFFFF" if preferences.fullscreen else "#9FA0A3"
                text _("Fullscreen"):
                    size 14
                    font "fonts/Montserrat-Medium.ttf"
                    color fullscreen_color



    # Skip settings

    frame at settings_frame_appear:
        background "assets/settings/images/skip_ntr.png"
        xpos 782 ypos 279
        xysize (1100, 80)

        text _("Skip") xpos 20 yalign 0.5 size 16 font "fonts/Montserrat-SemiBold.ttf"

        # Skip options
        hbox:
            xpos 720 yalign 0.5
            spacing 15

            # All Text button
            button at button_hover:
                action Function(toggle_skip_setting, "skip_unseen")
                text _("All Text") size 14 font "fonts/Montserrat-Medium.ttf":
                    color ("#FFFFFF" if _preferences.skip_unseen else "#9FA0A3")

            # Separator line
            add "assets/settings/images/line.png":
                yalign 0.5
                xysize(2, 20)

            # After Choices button
            button at button_hover:
                action Function(toggle_skip_setting, "skip_after_choices")
                text _("After Choices") size 14 font "fonts/Montserrat-Medium.ttf":
                    color ("#FFFFFF" if _preferences.skip_after_choices else "#9FA0A3")

            # Separator line
            add "assets/settings/images/line.png":
                yalign 0.5
                xysize(2, 20)

            # Transitions button
            button at button_hover:
                action Function(toggle_skip_setting, "transitions")
                text _("Transitions") size 14 font "fonts/Montserrat-Medium.ttf":
                    color ("#FFFFFF" if not _preferences.transitions else "#9FA0A3")


    # NTR settings
    frame at settings_frame_appear:
        background "assets/settings/images/skip_ntr.png"
        xpos 782 ypos 379  # 100 pixels below skip settings
        xysize (1100, 80)

        text _("NTR") xpos 20 yalign 0.5 size 16 font "fonts/Montserrat-SemiBold.ttf"

        # NTR options
        hbox:
            xpos 950 yalign 0.5
            spacing 15

            # Yes button
            button at button_hover:
                action [SetField(persistent, "ntr_enabled", True), SetField(_preferences, "ntr_enabled", True)]
                text _("Yes") size 14 font "fonts/Montserrat-Medium.ttf":
                    color ("#FFFFFF" if persistent.ntr_enabled else "#9FA0A3")

            # Separator line
            add "assets/settings/images/line.png":
                yalign 0.5
                xysize(2, 20)

            # No button
            button at button_hover:
                action [SetField(persistent, "ntr_enabled", False), SetField(_preferences, "ntr_enabled", False)]
                text _("No") size 14 font "fonts/Montserrat-Medium.ttf":
                    color ("#FFFFFF" if not persistent.ntr_enabled else "#9FA0A3")


    # Language settings

    frame at settings_frame_appear:
        background "assets/settings/images/lang_settings.png"
        xpos 1222 ypos 179
        xysize (660, 80)


        text _("Language") xpos 20 yalign 0.5 size 16 font "fonts/Montserrat-SemiBold.ttf"


        hbox:
            xpos 500 yalign 0.5
            spacing 10

            $ is_russian = _preferences.language == "russian"
            $ is_english = not is_russian
            $ ru_bg = "assets/settings/images/lang_hover.png" if is_russian else "assets/settings/images/lang_idle.png"
            $ en_bg = "assets/settings/images/lang_hover.png" if is_english else "assets/settings/images/lang_idle.png"
            $ ru_color = "#000000" if is_russian else "#FFFFFF"
            $ en_color = "#000000" if is_english else "#FFFFFF"

            button at button_hover:
                xysize (58, 42)
                background ru_bg
                text _("RU"):
                    size 14 
                    font "fonts/Montserrat-SemiBold.ttf" 
                    align (0.5, 0.5) 
                    color ru_color
                action Function(switch_language, "russian")

            button at button_hover:
                xysize (58, 42)
                background en_bg
                text _("ENG"):
                    size 14 
                    font "fonts/Montserrat-SemiBold.ttf" 
                    align (0.5, 0.5) 
                    color en_color
                action Function(switch_language, "english")

    # Music settings

    frame: 
        background "assets/settings/images/music_bg.png"
        xysize (475, 80)
        xpos 782 ypos 501

        text _("Music") xpos 20 yalign 0.5 size 16 font "fonts/Montserrat-SemiBold.ttf"


        bar value Preference("Music Volume"): 
            left_bar "assets/settings/images/bar-hover.png"
            right_bar "assets/settings/images/bar-idle.png"
            thumb "assets/settings/images/bar-cricle.png"
            thumb_offset 10
            xysize (164, 18)  
            xalign 0.9 yalign 0.5

    # Sound settings

    frame: 
        background "assets/settings/images/music_bg.png"
        xysize (475, 80)
        xpos 1277 ypos 501

        text _("Sound") xpos 20 yalign 0.5 size 16 font "fonts/Montserrat-SemiBold.ttf"


        bar value Preference("Sound Volume"): 
            left_bar "assets/settings/images/bar-hover.png"
            right_bar "assets/settings/images/bar-idle.png"
            thumb "assets/settings/images/bar-cricle.png"
            thumb_offset 10
            xysize (164, 18)  
            xalign 0.9 yalign 0.5

    
    # All mute
    if persistent.mute_button_selected:
        button at button_hover:
            background "assets/settings/images/mute_hover.png"
            xysize (109, 80)
            xpos 1773 ypos 501
            action [
                Preference("all mute", "toggle"),
                ToggleVariable("persistent.mute_button_selected"),
                With(dissolve)
            ]
            
            text _("Unmute"):
                align (0.5, 0.5)
                size 16
                font "fonts/Montserrat-SemiBold.ttf"
                color "#000000"
    else:
        button at button_hover:
            background "assets/settings/images/mute_idle.png"
            xysize (109, 80)
            xpos 1773 ypos 501
            action [
                Preference("all mute", "toggle"),
                ToggleVariable("persistent.mute_button_selected"),
                With(dissolve)
            ]
            
            text _("Mute"):
                align (0.5, 0.5)
                size 16
                font "fonts/Montserrat-SemiBold.ttf"
                color "#000000"

    frame: 
        background "assets/settings/images/text_animation.png"
        xysize (540, 80) 
        xpos 782 ypos 623

        text _("Text Speed") xpos 20 yalign 0.5 size 16 font "fonts/Montserrat-SemiBold.ttf"


        bar value Preference("text speed"): 
            left_bar "assets/settings/images/bar-hover.png"
            right_bar "assets/settings/images/bar-idle.png"
            thumb "assets/settings/images/bar-cricle.png"
            thumb_offset 10
            xysize (164, 18)  
            xalign 0.9 yalign 0.5

    frame: 
        background "assets/settings/images/text_animation.png"
        xysize (540, 80) 
        xpos 1342 ypos 623

        text _("Auto-Forward Speed") xpos 20 yalign 0.5 size 16 font "fonts/Montserrat-SemiBold.ttf"


        bar value Preference("auto-forward time"): 
            left_bar "assets/settings/images/bar-hover.png"
            right_bar "assets/settings/images/bar-idle.png"
            thumb "assets/settings/images/bar-cricle.png"
            thumb_offset 10
            xysize (164, 18)  
            xalign 0.9 yalign 0.5


    use navigation()